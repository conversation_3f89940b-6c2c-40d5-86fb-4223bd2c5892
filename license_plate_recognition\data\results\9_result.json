{"source": "images\\9.jpg", "success": false, "detections": [{"bbox": [522, 381, 259, 194], "confidence": 0.8707027435302734, "class": "license_plate", "area": 49181}, {"bbox": [463, 128, 259, 194], "confidence": 0.8447789549827576, "class": "license_plate", "area": -13464}, {"bbox": [0, 112, 116, 194], "confidence": 0.8096482753753662, "class": "license_plate", "area": 9512}, {"bbox": [87, 140, 259, 194], "confidence": 0.6513708829879761, "class": "license_plate", "area": 9288}], "recognition_results": [], "plate_text": "", "confidence": 0, "processing_stages": {"detection": 4, "plate_extraction": true}, "error": "識別過程出錯: OpenCV(4.10.0) D:\\a\\opencv-python\\opencv-python\\opencv\\modules\\imgproc\\src\\color.cpp:196: error: (-215:<PERSON><PERSON><PERSON> failed) !_src.empty() in function 'cv::cvtColor'\n", "processing_time": 0.20236587524414062}