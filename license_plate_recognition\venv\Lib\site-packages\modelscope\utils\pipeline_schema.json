{"acoustic-echo-cancellation": {"input": {"type": "object", "properties": {"nearend_mic": {"type": "string", "description": "Base64 encoded audio file or url string.."}, "farend_speech": {"type": "string", "description": "Base64 encoded audio file or url string.."}}}, "parameters": {}, "output": {"type": "object", "properties": {"output_pcm": {"type": "string", "description": "The base64 encoded PCM."}}}}, "acoustic-noise-suppression": {"input": {"type": "object", "properties": {"audio": {"type": "string", "description": "Base64 encoded audio file or url string.."}}}, "parameters": {}, "output": {"type": "object", "properties": {"output_pcm": {"type": "string", "description": "The base64 encoded PCM."}}}}, "action-detection": {"input": {"type": "object", "properties": {"video": {"type": "string", "description": "Base64 encoded video file or url string.."}}}, "parameters": {}, "output": {"type": "object", "properties": {"timestamps": {"type": "string"}, "labels": {"type": "array", "items": {"type": "string"}}, "scores": {"type": "array", "items": {"type": "number"}}, "boxes": {"type": "array", "items": {"type": "number"}}}}}, "action-recognition": {"input": {"type": "object", "properties": {"video": {"type": "string", "description": "Base64 encoded video file or url string.."}}}, "parameters": {}, "output": {"type": "object", "properties": {"labels": {"type": "array", "items": {"type": "string"}}}}}, "animal-recognition": {"input": {}, "parameters": {}, "output": {"type": "object"}}, "auto-speech-recognition": {"input": {"type": "object", "properties": {"wav": {"type": "string", "description": "Base64 encoded audio file or url string.."}, "text": {"type": "string", "description": "The input text."}}}, "parameters": {}, "output": {"type": "object", "properties": {"text": {"type": "string"}}}}, "audio-quantization": {"input": {"type": "object", "properties": {"wav": {"type": "string", "description": "Base64 encoded audio file or url string.."}}}, "parameters": {}, "output": {"type": "object", "properties": {"output_wav": {"type": "string", "description": "The base64 encoded WAV."}}}}, "bad-image-detecting": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"scores": {"type": "array", "items": {"type": "number"}}, "labels": {"type": "array", "items": {"type": "string"}}}}}, "body-2d-keypoints": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"keypoints": {"type": "array", "items": {"type": "number"}}, "scores": {"type": "array", "items": {"type": "number"}}, "boxes": {"type": "array", "items": {"type": "number"}}}}}, "body-3d-keypoints": {"input": {"type": "object", "properties": {"video": {"type": "string", "description": "Base64 encoded video file or url string.."}}}, "parameters": {}, "output": {"type": "object", "properties": {"keypoints": {"type": "array", "items": {"type": "number"}}, "timestamps": {"type": "string"}, "output_video": {"type": "string", "description": "The base64 encoded video."}}}}, "card-detection": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"scores": {"type": "array", "items": {"type": "number"}}, "boxes": {"type": "array", "items": {"type": "number"}}, "keypoints": {"type": "array", "items": {"type": "number"}}}}}, "chat": {"input": {"type": "object", "properties": {"text": {"type": "string", "description": "The input text."}, "history": {"type": "array"}}}, "parameters": {}, "output": {"type": "object", "properties": {"response": {"type": "object"}, "history": {"type": "object"}}}}, "code-generation": {"input": {}, "parameters": {}, "output": {"type": "object"}}, "code-translation": {"input": {}, "parameters": {}, "output": {"type": "object"}}, "competency-aware-translation": {"input": {"type": "object", "properties": {"text": {"type": "string", "description": "The input text."}}}, "parameters": {}, "output": {"type": "object"}}, "controllable-image-generation": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}, "prompt": {"type": "string", "description": "The input text."}}}, "parameters": {}, "output": {"type": "object", "properties": {"output_img": {"type": "string", "description": "The base64 encoded image."}}}}, "crowd-counting": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"scores": {"type": "array", "items": {"type": "number"}}, "output_img": {"type": "string", "description": "The base64 encoded image."}}}}, "document-grounded-dialog-generate": {"input": {"type": "object", "properties": {"query": {"type": "array"}, "context": {"type": "array"}, "label": {"type": "array"}}}, "parameters": {}, "output": {"type": "object", "properties": {"text": {"type": "string"}}}}, "document-grounded-dialog-rerank": {"input": {"type": "object", "properties": {"dataset": {"type": "array"}}}, "parameters": {}, "output": {"type": "object", "properties": {"output": {"type": "object"}}}}, "document-grounded-dialog-retrieval": {"input": {"type": "object", "properties": {"query": {"type": "array"}, "positive": {"type": "array"}, "negative": {"type": "array"}}}, "parameters": {}, "output": {"type": "object", "properties": {"output": {"type": "object"}}}}, "document-segmentation": {"input": {}, "parameters": {}, "output": {"type": "object"}}, "document-vl-embedding": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"img_embedding": {"type": "array", "items": {"type": "number"}}, "text_embedding": {"type": "array", "items": {"type": "number"}}}}}, "domain-specific-object-detection": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"scores": {"type": "array", "items": {"type": "number"}}, "labels": {"type": "array", "items": {"type": "string"}}, "boxes": {"type": "array", "items": {"type": "number"}}}}}, "efficient-diffusion-tuning": {"input": {}, "parameters": {}, "output": {"type": "object"}}, "extractive-summarization": {"input": {}, "parameters": {}, "output": {"type": "object"}}, "face-2d-keypoints": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"keypoints": {"type": "array", "items": {"type": "number"}}, "poses": {"type": "array", "items": {"type": "array", "items": {"type": "number"}}}, "boxes": {"type": "array", "items": {"type": "number"}}}}}, "face-attribute-recognition": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"scores": {"type": "array", "items": {"type": "number"}}, "labels": {"type": "array", "items": {"type": "string"}}}}}, "face-detection": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"scores": {"type": "array", "items": {"type": "number"}}, "boxes": {"type": "array", "items": {"type": "number"}}, "keypoints": {"type": "array", "items": {"type": "number"}}}}}, "face-emotion": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"output": {"type": "object"}, "boxes": {"type": "array", "items": {"type": "number"}}}}}, "face-human-hand-detection": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"labels": {"type": "array", "items": {"type": "string"}}, "boxes": {"type": "array", "items": {"type": "number"}}, "scores": {"type": "array", "items": {"type": "number"}}}}}, "face-image-generation": {"input": {"type": "object", "properties": {"number": {"type": "integer"}}}, "parameters": {}, "output": {"type": "object", "properties": {"output_img": {"type": "string", "description": "The base64 encoded image."}}}}, "face-liveness": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"scores": {"type": "array", "items": {"type": "number"}}, "boxes": {"type": "array", "items": {"type": "number"}}}}}, "face-quality-assessment": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"scores": {"type": "array", "items": {"type": "number"}}, "boxes": {"type": "array", "items": {"type": "number"}}}}}, "face-recognition": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"img_embedding": {"type": "array", "items": {"type": "number"}}}}}, "face-reconstruction": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"output": {"type": "object"}}}}, "facial-expression-recognition": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"scores": {"type": "array", "items": {"type": "number"}}, "labels": {"type": "array", "items": {"type": "string"}}}}}, "faq-question-answering": {"input": {"type": "object", "properties": {"query_set": {"type": "array"}, "support_set": {"type": "array"}}}, "parameters": {}, "output": {"type": "object", "properties": {"output": {"type": "object"}}}}, "feature-extraction": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"text_embedding": {"type": "array", "items": {"type": "number"}}}}}, "fid-dialogue": {"input": {"type": "object", "properties": {"history": {"type": "string", "description": "The input text."}, "knowledge": {"type": "string", "description": "The input text."}, "bot_profile": {"type": "string", "description": "The input text."}, "user_profile": {"type": "string", "description": "The input text."}}}, "parameters": {}, "output": {"type": "object", "properties": {"text": {"type": "string"}}}}, "fill-mask": {"input": {"type": "object", "properties": {"text": {"type": "string", "description": "The input text."}}}, "parameters": {}, "output": {"type": "object", "properties": {"text": {"type": "string"}}}}, "general-recognition": {"input": {}, "parameters": {}, "output": {"type": "object"}}, "generative-multi-modal-embedding": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}, "text": {"type": "string", "description": "The input text."}}}, "parameters": {}, "output": {"type": "object", "properties": {"img_embedding": {"type": "array", "items": {"type": "number"}}, "text_embedding": {"type": "array", "items": {"type": "number"}}, "caption": {"type": "string"}}}}, "hand-static": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"output": {"type": "object"}}}}, "human-detection": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"scores": {"type": "array", "items": {"type": "number"}}, "labels": {"type": "array", "items": {"type": "string"}}, "boxes": {"type": "array", "items": {"type": "number"}}}}}, "human-reconstruction": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"output": {"type": "object"}}}}, "image-body-reshaping": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"output_img": {"type": "string", "description": "The base64 encoded image."}}}}, "image-captioning": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"caption": {"type": "string"}}}}, "image-classification": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"scores": {"type": "array", "items": {"type": "number"}}, "labels": {"type": "array", "items": {"type": "string"}}}}}, "image-color-enhancement": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"output_img": {"type": "string", "description": "The base64 encoded image."}}}}, "image-colorization": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"output_img": {"type": "string", "description": "The base64 encoded image."}}}}, "image-debanding": {"input": {}, "parameters": {}, "output": {"type": "object"}}, "image-deblurring": {"input": {}, "parameters": {}, "output": {"type": "object"}}, "image-demoireing": {"input": {}, "parameters": {}, "output": {"type": "object"}}, "image-denoising": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"output_img": {"type": "string", "description": "The base64 encoded image."}}}}, "image-depth-estimation": {"input": {}, "parameters": {}, "output": {"type": "object"}}, "dense-optical-flow-estimation": {"input": {}, "parameters": {}, "output": {"type": "object"}}, "image-normal-estimation": {"input": {}, "parameters": {}, "output": {"type": "object"}}, "human-normal-estimation": {"input": {}, "parameters": {}, "output": {"type": "object"}}, "image-driving-perception": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"boxes": {"type": "array", "items": {"type": "number"}}, "masks": {"type": "array", "items": {"type": "number"}}}}}, "image-face-fusion": {"input": {"type": "object", "properties": {"template": {"type": "string", "description": "Base64 encoded image file or url string."}, "user": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {"type": "object", "properties": {"user": {"type": "object", "default": null}}}, "output": {"type": "object", "properties": {"output_img": {"type": "string", "description": "The base64 encoded image."}}}}, "image-fewshot-detection": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object"}}, "image-inpainting": {"input": {"type": "object", "properties": {"img": {"type": "string", "description": "Base64 encoded image file or url string."}, "mask": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"output_img": {"type": "string", "description": "The base64 encoded image."}}}}, "image-matching": {"input": {}, "parameters": {}, "output": {"type": "object"}}, "image-local-feature-matching": {"input": {}, "parameters": {}, "output": {"type": "object"}}, "image-multi-view-depth-estimation": {"input": {}, "parameters": {}, "output": {"type": "object"}}, "image-object-detection": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"scores": {"type": "array", "items": {"type": "number"}}, "labels": {"type": "array", "items": {"type": "string"}}, "boxes": {"type": "array", "items": {"type": "number"}}}}}, "image-paintbyexample": {"input": {"type": "object", "properties": {"img": {"type": "string", "description": "Base64 encoded image file or url string."}, "mask": {"type": "string", "description": "Base64 encoded image file or url string."}, "reference": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"output_img": {"type": "string", "description": "The base64 encoded image."}}}}, "image-portrait-enhancement": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"output_img": {"type": "string", "description": "The base64 encoded image."}}}}, "image-portrait-stylization": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"output_img": {"type": "string", "description": "The base64 encoded image."}}}}, "image-quality-assessment-degradation": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"scores": {"type": "array", "items": {"type": "number"}}, "labels": {"type": "array", "items": {"type": "string"}}}}}, "image-quality-assessment-mos": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"score": {"type": "number"}}}}, "image-reid-person": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"img_embedding": {"type": "array", "items": {"type": "number"}}}}}, "image-segmentation": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {"type": "object", "properties": {"min_size": {"type": "integer", "default": 640}, "max_size": {"type": "integer", "default": 1333}, "score_thr": {"type": "number", "default": 0}}}, "output": {"type": "object", "properties": {"scores": {"type": "array", "items": {"type": "number"}}, "labels": {"type": "array", "items": {"type": "string"}}, "masks": {"type": "array", "items": {"type": "number"}}}}}, "image-skychange": {"input": {"type": "object", "properties": {"sky_image": {"type": "string", "description": "Base64 encoded image file or url string."}, "scene_image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"output_img": {"type": "string", "description": "The base64 encoded image."}}}}, "image-style-transfer": {"input": {"type": "object", "properties": {"content": {"type": "string", "description": "Base64 encoded image file or url string."}, "style": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {"type": "object", "properties": {"style": {"type": "object", "default": null}}}, "output": {"type": "object", "properties": {"output_img": {"type": "string", "description": "The base64 encoded image."}}}}, "image-super-resolution": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"output_img": {"type": "string", "description": "The base64 encoded image."}}}}, "image-text-retrieval": {"input": {}, "parameters": {}, "output": {"type": "object"}}, "image-to-image-generation": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"output_img": {"type": "string", "description": "The base64 encoded image."}}}}, "image-to-image-translation": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"output_img": {"type": "string", "description": "The base64 encoded image."}}}}, "image-try-on": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"output_img": {"type": "string", "description": "The base64 encoded image."}}}}, "indoor-layout-estimation": {"input": {}, "parameters": {}, "output": {"type": "object"}}, "information-extraction": {"input": {}, "parameters": {}, "output": {"type": "object"}}, "inverse-text-processing": {"input": {"type": "object", "properties": {"text": {"type": "string", "description": "The input text."}}}, "parameters": {}, "output": {"type": "object", "properties": {"text": {"type": "string"}}}}, "keyword-spotting": {"input": {"type": "object", "properties": {"audio": {"type": "string", "description": "Base64 encoded audio file or url string.."}}}, "parameters": {}, "output": {"type": "object", "properties": {"kws_list": {"type": "array", "items": {"type": "string"}}}}}, "language-guided-video-summarization": {"input": {}, "parameters": {}, "output": {"type": "object"}}, "language-score-prediction": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"text": {"type": "string"}}}}, "license-plate-detection": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"polygons": {"type": "array", "items": {"type": "number"}}, "text": {"type": "string"}}}}, "lineless-table-recognition": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"polygons": {"type": "array", "items": {"type": "number"}}, "boxes": {"type": "array", "items": {"type": "number"}}}}}, "live-category": {"input": {"type": "object", "properties": {"video": {"type": "string", "description": "Base64 encoded video file or url string.."}}}, "parameters": {}, "output": {"type": "object", "properties": {"scores": {"type": "array", "items": {"type": "number"}}, "labels": {"type": "array", "items": {"type": "string"}}}}}, "motion-generation": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"keypoints": {"type": "array", "items": {"type": "number"}}, "output_video": {"type": "string", "description": "The base64 encoded video."}}}}, "movie-scene-segmentation": {"input": {"type": "object", "properties": {"video": {"type": "string", "description": "Base64 encoded video file or url string.."}}}, "parameters": {}, "output": {"type": "object", "properties": {"shot_num": {"type": "integer"}, "shot_meta_list": {"type": "array", "items": {"type": "integer"}}, "scene_num": {"type": "integer"}, "scene_meta_list": {"type": "array", "items": {"type": "integer"}}}}}, "multi-modal-embedding": {"input": {"type": "object", "properties": {"img": {"type": "string", "description": "Base64 encoded image file or url string."}, "text": {"type": "string", "description": "The input text."}}}, "parameters": {}, "output": {"type": "object", "properties": {"img_embedding": {"type": "array", "items": {"type": "number"}}, "text_embedding": {"type": "array", "items": {"type": "number"}}}}}, "multi-modal-similarity": {"input": {"type": "object", "properties": {"img": {"type": "string", "description": "Base64 encoded image file or url string."}, "text": {"type": "string", "description": "The input text."}}}, "parameters": {}, "output": {"type": "object", "properties": {"img_embedding": {"type": "array", "items": {"type": "number"}}, "text_embedding": {"type": "array", "items": {"type": "number"}}, "scores": {"type": "array", "items": {"type": "number"}}}}}, "multimodal-dialogue": {"input": {"type": "object", "properties": {"messages": {"type": "array"}}}, "parameters": {}, "output": {"type": "object", "properties": {"text": {"type": "string"}}}}, "named-entity-recognition": {"input": {"type": "object", "properties": {"text": {"type": "string", "description": "The input text."}}}, "parameters": {}, "output": {"type": "object", "properties": {"output": {"type": "object"}}}}, "nerf-recon-4k": {"input": {}, "parameters": {}, "output": {"type": "object"}}, "nerf-recon-acc": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"output": {"type": "object"}}}}, "nerf-recon-vq-compression": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"output": {"type": "object"}}}}, "nli": {"input": {"type": "array", "items": {"type": "string", "description": "The input text."}}, "parameters": {"type": "object", "properties": {"topk": {"type": "integer", "default": null}}}, "output": {"type": "object", "properties": {"scores": {"type": "array", "items": {"type": "number"}}, "labels": {"type": "array", "items": {"type": "string"}}}}}, "object-detection-3d": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"output_img": {"type": "string", "description": "The base64 encoded image."}}}}, "ocr-detection": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"polygons": {"type": "array", "items": {"type": "number"}}}}}, "ocr-recognition": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"text": {"type": "string"}}}}, "open-vocabulary-detection": {"input": {"type": "object", "properties": {"img": {"type": "string", "description": "Base64 encoded image file or url string."}, "category_names": {"type": "string", "description": "The input text."}}}, "parameters": {}, "output": {"type": "object", "properties": {"scores": {"type": "array", "items": {"type": "number"}}, "labels": {"type": "array", "items": {"type": "string"}}, "boxes": {"type": "array", "items": {"type": "number"}}}}}, "panorama-depth-estimation": {"input": {}, "parameters": {}, "output": {"type": "object"}}, "part-of-speech": {"input": {"type": "object", "properties": {"text": {"type": "string", "description": "The input text."}}}, "parameters": {}, "output": {"type": "object", "properties": {"output": {"type": "object"}}}}, "pedestrian-attribute-recognition": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"boxes": {"type": "array", "items": {"type": "number"}}, "labels": {"type": "array", "items": {"type": "string"}}}}}, "pointcloud-sceneflow-estimation": {"input": {}, "parameters": {}, "output": {"type": "object"}}, "portrait-matting": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"output_img": {"type": "string", "description": "The base64 encoded image."}}}}, "product-retrieval-embedding": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"img_embedding": {"type": "array", "items": {"type": "number"}}}}}, "product-segmentation": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"masks": {"type": "array", "items": {"type": "number"}}}}}, "protein-structure": {"input": {}, "parameters": {}, "output": {"type": "object"}}, "punctuation": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"text": {"type": "string"}}}}, "referring-video-object-segmentation": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"masks": {"type": "array", "items": {"type": "number"}}, "timestamps": {"type": "string"}, "output_video": {"type": "string", "description": "The base64 encoded video."}}}}, "relation-extraction": {"input": {"type": "object", "properties": {"text": {"type": "string", "description": "The input text."}}}, "parameters": {}, "output": {"type": "object", "properties": {"spo_list": {"type": "array", "items": {"type": "number"}}}}}, "semantic-segmentation": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"masks": {"type": "array", "items": {"type": "number"}}}}}, "sentence-embedding": {"input": {"type": "object", "properties": {"source_sentence": {"type": "array"}, "sentences_to_compare": {"type": "array"}}}, "parameters": {}, "output": {"type": "object", "properties": {"text_embedding": {"type": "array", "items": {"type": "number"}}, "scores": {"type": "array", "items": {"type": "number"}}}}}, "sentence-similarity": {"input": {"type": "array", "items": {"type": "string", "description": "The input text."}}, "parameters": {"type": "object", "properties": {"topk": {"type": "integer", "default": null}}}, "output": {"type": "object", "properties": {"scores": {"type": "array", "items": {"type": "number"}}, "labels": {"type": "array", "items": {"type": "string"}}}}}, "sentiment-classification": {"input": {"type": "object", "properties": {"text": {"type": "string", "description": "The input text."}}}, "parameters": {"type": "object", "properties": {"topk": {"type": "integer", "default": null}}}, "output": {"type": "object", "properties": {"scores": {"type": "array", "items": {"type": "number"}}, "labels": {"type": "array", "items": {"type": "string"}}}}}, "shop-segmentation": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"masks": {"type": "array", "items": {"type": "number"}}}}}, "siamese-uie": {"input": {}, "parameters": {}, "output": {"type": "object"}}, "skin-retouching": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"output_img": {"type": "string", "description": "The base64 encoded image."}}}}, "speaker-diarization": {"input": {}, "parameters": {}, "output": {"type": "object"}}, "speaker-diarization-dialogue-detection": {"input": {"type": "object", "properties": {"text": {"type": "string", "description": "The input text."}}}, "parameters": {"type": "object", "properties": {"topk": {"type": "integer", "default": null}}}, "output": {"type": "object", "properties": {"scores": {"type": "array", "items": {"type": "number"}}, "labels": {"type": "array", "items": {"type": "string"}}}}}, "speaker-diarization-semantic-speaker-turn-detection": {"input": {"type": "object", "properties": {"text": {"type": "string", "description": "The input text."}}}, "parameters": {}, "output": {"type": "object", "properties": {"logits": {"type": "array", "items": {"type": "number"}}, "text": {"type": "string"}, "prediction": {"type": "array", "items": {"type": "number"}}}}}, "speaker-verification": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"scores": {"type": "array", "items": {"type": "number"}}}}}, "speech-language-recognition": {"input": {"type": "object", "properties": {"audio": {"type": "string", "description": "Base64 encoded audio file or url string.."}}}, "parameters": {}, "output": {"type": "object", "properties": {"text": {"type": "string"}}}}, "speech-separation": {"input": {"type": "object", "properties": {"audio": {"type": "string", "description": "Base64 encoded audio file or url string.."}}}, "parameters": {}, "output": {"type": "object", "properties": {"output_pcm_list": {"type": "array", "items": {"type": "string", "description": "The base64 encoded PCM."}}}}}, "speech-timestamp": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"text": {"type": "string"}}}}, "sudoku": {"input": {"type": "object", "properties": {"text": {"type": "string", "description": "The input text."}}}, "parameters": {}, "output": {"type": "object", "properties": {"text": {"type": "string"}}}}, "table-question-answering": {"input": {"type": "object", "properties": {"question": {"type": "string", "description": "The input text."}, "history_sql": {"type": "object"}}}, "parameters": {}, "output": {"type": "object", "properties": {"output": {"type": "object"}}}}, "table-recognition": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"polygons": {"type": "array", "items": {"type": "number"}}}}}, "task-oriented-conversation": {"input": {"type": "object", "properties": {"user_input": {"type": "string", "description": "The input text."}, "history": {"type": "object"}}}, "parameters": {}, "output": {"type": "object", "properties": {"output": {"type": "object"}}}}, "task-template": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}, "text": {"type": "string", "description": "The input text."}}}, "parameters": {"type": "object", "properties": {"max_length": {"type": "integer", "default": 1024}, "top_p": {"type": "number", "default": 0.8}, "postprocess_param1": {"type": "string", "default": null}}}, "output": {"type": "object", "properties": {"boxes": {"type": "array", "items": {"type": "number"}}, "output_img": {"type": "string", "description": "The base64 encoded image."}, "text_embedding": {"type": "array", "items": {"type": "number"}}}}}, "text-classification": {"input": {"type": "object", "properties": {"text": {"type": "string", "description": "The input text."}, "text2": {"type": "string", "description": "The input text."}}}, "parameters": {"type": "object", "properties": {"topk": {"type": "integer", "default": null}}}, "output": {"type": "object", "properties": {"scores": {"type": "array", "items": {"type": "number"}}, "labels": {"type": "array", "items": {"type": "string"}}}}}, "text-driven-segmentation": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}, "text": {"type": "string", "description": "The input text."}}}, "parameters": {}, "output": {"type": "object", "properties": {"masks": {"type": "array", "items": {"type": "number"}}}}}, "text-error-correction": {"input": {"type": "object", "properties": {"text": {"type": "string", "description": "The input text."}}}, "parameters": {}, "output": {"type": "object", "properties": {"output": {"type": "object"}}}}, "text-generation": {"input": {"type": "object", "properties": {"text": {"type": "string", "description": "The input text."}}}, "parameters": {"type": "object", "properties": {"max_length": {"type": "integer", "default": 50}, "do_sample": {"type": "boolean", "default": true}, "top_p": {"type": "number", "default": 0.85}, "temperature": {"type": "number", "default": 1}, "repetition_penalty": {"type": "number", "default": 1}, "eos_token_id": {"type": "integer", "default": 2}, "bos_token_id": {"type": "integer", "default": 1}, "pad_token_id": {"type": "integer", "default": 0}}}, "output": {"type": "object", "properties": {"text": {"type": "string"}}}}, "text-ranking": {"input": {"type": "object", "properties": {"source_sentence": {"type": "array"}, "sentences_to_compare": {"type": "array"}}}, "parameters": {}, "output": {"type": "object", "properties": {"scores": {"type": "array", "items": {"type": "number"}}}}}, "text-summarization": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"text": {"type": "string"}}}}, "text-to-360panorama-image": {"input": {"type": "object", "properties": {"prompt": {"type": "string", "description": "The input text."}}}, "parameters": {}, "output": {"type": "object", "properties": {"output_img": {"type": "string", "description": "The base64 encoded image."}}}}, "text-to-image-synthesis": {"input": {"type": "object", "properties": {"text": {"type": "string", "description": "The input text."}}}, "parameters": {"type": "object", "properties": {"init": {"type": "object", "default": null}, "init_scale": {"type": "integer", "default": 2000}, "skip_steps": {"type": "integer", "default": 10}, "randomize_class": {"type": "boolean", "default": true}, "eta": {"type": "number", "default": 0.8}, "output_type": {"type": "string", "default": "pil"}, "return_dict": {"type": "boolean", "default": true}, "clip_guidance_scale": {"type": "integer", "default": 7500}}}, "output": {"type": "object", "properties": {"output_imgs": {"type": "array", "items": {"type": "string", "description": "The base64 encoded image."}}}}}, "text-to-speech": {"input": {"type": "object", "properties": {"text": {"type": "string", "description": "The input text."}}}, "parameters": {}, "output": {"type": "object", "properties": {"output_wav": {"type": "string", "description": "The base64 encoded WAV."}}}}, "text-to-video-synthesis": {"input": {"type": "object", "properties": {"text": {"type": "string", "description": "The input text."}}}, "parameters": {}, "output": {"type": "object", "properties": {"output_video": {"type": "string", "description": "The base64 encoded video."}}}}, "text2sql": {"input": {"type": "object", "properties": {"text": {"type": "string", "description": "The input text."}, "database": {"type": "string", "description": "The input text."}}}, "parameters": {}, "output": {"type": "object", "properties": {"text": {"type": "string"}}}}, "text2text-generation": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"text": {"type": "string"}}}}, "token-classification": {"input": {}, "parameters": {}, "output": {"type": "object"}}, "translation": {"input": {"type": "object", "properties": {"text": {"type": "string", "description": "The input text."}}}, "parameters": {}, "output": {"type": "object", "properties": {"translation": {"type": "string"}}}}, "translation-evaluation": {"input": {"type": "object", "properties": {"hyp": {"type": "array"}, "src": {"type": "array"}, "ref": {"type": "array"}}}, "parameters": {}, "output": {"type": "object", "properties": {"score": {"type": "number"}}}}, "universal-matting": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"output_img": {"type": "string", "description": "The base64 encoded image."}}}}, "video-captioning": {"input": {"type": "object", "properties": {"video": {"type": "string", "description": "Base64 encoded video file or url string.."}}}, "parameters": {}, "output": {"type": "object", "properties": {"caption": {"type": "string"}}}}, "video-category": {"input": {"type": "object", "properties": {"video": {"type": "string", "description": "Base64 encoded video file or url string.."}}}, "parameters": {}, "output": {"type": "object", "properties": {"scores": {"type": "array", "items": {"type": "number"}}, "labels": {"type": "array", "items": {"type": "string"}}}}}, "video-colorization": {"input": {"type": "object", "properties": {"video": {"type": "string", "description": "Base64 encoded video file or url string.."}}}, "parameters": {}, "output": {"type": "object", "properties": {"output_video": {"type": "string", "description": "The base64 encoded video."}}}}, "video-deinterlace": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"output_video": {"type": "string", "description": "The base64 encoded video."}}}}, "video-depth-estimation": {"input": {}, "parameters": {}, "output": {"type": "object"}}, "video-embedding": {"input": {"type": "object", "properties": {"video": {"type": "string", "description": "Base64 encoded video file or url string.."}}}, "parameters": {}, "output": {"type": "object", "properties": {"video_embedding": {"type": "array", "items": {"type": "number"}}}}}, "video-frame-interpolation": {"input": {"type": "object", "properties": {"out_fps": {"type": "number", "default": 0}}}, "parameters": {}, "output": {"type": "object", "properties": {"output_video": {"type": "string", "description": "The base64 encoded video."}}}}, "video-human-matting": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"masks": {"type": "array", "items": {"type": "number"}}, "output_video": {"type": "string", "description": "The base64 encoded video."}}}}, "video-inpainting": {"input": {"type": "object", "properties": {"video_input_path": {"type": "string", "description": "The input text."}, "video_output_path": {"type": "string", "description": "The input text."}, "mask_path": {"type": "string", "description": "The input text."}}}, "parameters": {}, "output": {"type": "object", "properties": {"output": {"type": "object"}}}}, "video-instance-segmentation": {"input": {}, "parameters": {}, "output": {"type": "object"}}, "video-multi-modal-embedding": {"input": {}, "parameters": {}, "output": {"type": "object"}}, "video-multi-object-tracking": {"input": {"type": "object", "properties": {"video": {"type": "string", "description": "Base64 encoded video file or url string.."}}}, "parameters": {}, "output": {"type": "object", "properties": {"boxes": {"type": "array", "items": {"type": "number"}}, "labels": {"type": "array", "items": {"type": "string"}}, "timestamps": {"type": "string"}}}}, "video-object-detection": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"scores": {"type": "array", "items": {"type": "number"}}, "labels": {"type": "array", "items": {"type": "string"}}, "boxes": {"type": "array", "items": {"type": "number"}}}}}, "video-object-segmentation": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"masks": {"type": "array", "items": {"type": "number"}}}}}, "video-panoptic-segmentation": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"scores": {"type": "array", "items": {"type": "number"}}, "labels": {"type": "array", "items": {"type": "string"}}, "masks": {"type": "array", "items": {"type": "number"}}, "boxes": {"type": "array", "items": {"type": "number"}}, "uuid": {"type": "string"}}}}, "video-question-answering": {"input": {"type": "object", "properties": {"video": {"type": "string", "description": "Base64 encoded video file or url string.."}, "text": {"type": "string", "description": "The input text."}}}, "parameters": {}, "output": {"type": "object", "properties": {"text": {"type": "string"}}}}, "video-single-object-tracking": {"input": {"type": "array", "items": {"type": "string", "description": "Base64 encoded video file or url string.."}}, "parameters": {}, "output": {"type": "object", "properties": {"boxes": {"type": "array", "items": {"type": "number"}}, "timestamps": {"type": "string"}}}}, "video-stabilization": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"output_video": {"type": "string", "description": "The base64 encoded video."}}}}, "video-summarization": {"input": {"type": "object", "properties": {"text": {"type": "string", "description": "The input text."}}}, "parameters": {}, "output": {"type": "object", "properties": {"output": {"type": "object"}}}}, "video-super-resolution": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"output_video": {"type": "string", "description": "The base64 encoded video."}}}}, "video-temporal-grounding": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"scores": {"type": "array", "items": {"type": "number"}}, "tbounds": {"type": "object"}}}}, "video-text-retrieval": {"input": {}, "parameters": {}, "output": {"type": "object"}}, "virtual-try-on": {"input": {"type": "array", "items": {"type": "string", "description": "Base64 encoded image file or url string."}}, "parameters": {}, "output": {"type": "object", "properties": {"output_img": {"type": "string", "description": "The base64 encoded image."}}}}, "vision-efficient-tuning": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}}}, "parameters": {}, "output": {"type": "object", "properties": {"scores": {"type": "array", "items": {"type": "number"}}, "labels": {"type": "array", "items": {"type": "string"}}}}}, "visual-entailment": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}, "text": {"type": "string", "description": "The input text."}, "text2": {"type": "string", "description": "The input text."}}}, "parameters": {}, "output": {"type": "object", "properties": {"scores": {"type": "array", "items": {"type": "number"}}, "labels": {"type": "array", "items": {"type": "string"}}}}}, "visual-grounding": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}, "text": {"type": "string", "description": "The input text."}}}, "parameters": {}, "output": {"type": "object", "properties": {"boxes": {"type": "array", "items": {"type": "number"}}, "scores": {"type": "array", "items": {"type": "number"}}}}}, "visual-question-answering": {"input": {"type": "object", "properties": {"image": {"type": "string", "description": "Base64 encoded image file or url string."}, "text": {"type": "string", "description": "The input text."}}}, "parameters": {}, "output": {"type": "object", "properties": {"text": {"type": "string"}}}}, "voice-activity-detection": {"input": {}, "parameters": {}, "output": {"type": "object"}}, "word-alignment": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"output": {"type": "object"}}}}, "word-segmentation": {"input": {"type": "object", "properties": {"text": {"type": "string", "description": "The input text."}}}, "parameters": {}, "output": {"type": "object", "properties": {"output": {"type": "object"}}}}, "zero-shot-classification": {"input": {"type": "object", "properties": {"text": {"type": "string", "description": "The input text."}}}, "parameters": {"type": "object", "properties": {"candidate_labels": {"type": "object"}, "multi_label": {"type": "boolean", "default": false}}}, "output": {"type": "object", "properties": {"scores": {"type": "array", "items": {"type": "number"}}, "labels": {"type": "array", "items": {"type": "string"}}}}}, "self-supervised-depth-completion": {"input": {}, "parameters": {}, "output": {"type": "object", "properties": {"output_img": {"type": "string", "description": "The base64 encoded image."}}}}}