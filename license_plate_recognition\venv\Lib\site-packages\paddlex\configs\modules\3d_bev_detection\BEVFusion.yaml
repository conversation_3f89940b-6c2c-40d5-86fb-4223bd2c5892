Global:
  model: BEVFusion
  mode: check_dataset
  dataset_dir: "/paddle/dataset/paddlex/3d/nuscenes_demo"
  device: gpu:0,1,2,3
  output: "output"
  load_cam_from: "https://paddle-model-ecology.bj.bcebos.com/paddlex/official_pretrained_model/BEVFusion_camera_pretrained.pdparams"
  load_lidar_from: "https://paddle-model-ecology.bj.bcebos.com/paddlex/official_pretrained_model/BEVFusion_lidar_pretrained.pdparams"
  datart_prefix: True
  version: "mini"

CheckDataset:
  convert:
    enable: False
  split:
    enable: False

Train:
  epochs_iters: 2
  batch_size: 2
  learning_rate: 0.001
  warmup_steps: 150

Evaluate:
  batch_size: 1
  weight_path: output/best_model/model.pdparams


Export:
  weight_path: "https://paddle-model-ecology.bj.bcebos.com/paddlex/official_pretrained_model/BEVFusion_pretrained.pdparams"


Predict:
  batch_size: 1
  model_dir: "output/best_model/inference"
  input: "https://paddle-model-ecology.bj.bcebos.com/paddlex/det_3d/demo_det_3d/nuscenes_demo_infer.tar"
  kernel_option:
    run_mode: paddle
