import cv2
import numpy as np
import re
from pathlib import Path
from utils.config import Config

class CharacterRecognizer:
    """字符識別器類"""
    
    def __init__(self, model_path=None):
        self.model_path = model_path or Config.OCR_MODEL_PATH
        self.confidence_threshold = Config.OCR_CONFIDENCE
        
        # 台灣車牌字符集
        self.characters = {
            'english': 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
            'numbers': '0123456789',
            'chinese': '臺灣省高雄市台北市桃園縣新竹縣苗栗縣台中縣彰化縣南投縣雲林縣嘉義縣台南縣高雄縣屏東縣台東縣花蓮縣宜蘭縣澎湖縣基隆市新竹市嘉義市台中市台南市'
        }
        
        # 載入模型
        self.model = self._load_model()
        
    def _load_model(self):
        """載入OCR模型"""
        try:
            # 嘗試使用PaddleOCR
            from paddleocr import PaddleOCR
            
            # 初始化PaddleOCR
            ocr = PaddleOCR(use_angle_cls=True, lang='ch', 
                          det=False,  # 只使用識別功能
                          rec=True,
                          use_gpu=False)
            
            return {'type': 'paddleocr', 'model': ocr}
            
        except ImportError:
            print("警告: 未安裝PaddleOCR，使用備用識別方法")
            return {'type': 'template', 'model': None}
        
        except Exception as e:
            print(f"載入OCR模型失敗: {e}")
            return {'type': 'template', 'model': None}
    
    def recognize_characters(self, character_images):
        """識別字符圖像"""
        if not character_images:
            return []
        
        try:
            if self.model['type'] == 'paddleocr':
                return self._recognize_with_paddleocr(character_images)
            else:
                return self._recognize_with_template(character_images)
                
        except Exception as e:
            print(f"字符識別出錯: {e}")
            return self._recognize_with_template(character_images)
    
    def _recognize_with_paddleocr(self, character_images):
        """使用PaddleOCR識別字符"""
        results = []
        ocr = self.model['model']
        
        for i, char_img in enumerate(character_images):
            try:
                # 轉換為3通道圖像
                if len(char_img.shape) == 2:
                    char_img = cv2.cvtColor(char_img, cv2.COLOR_GRAY2BGR)
                
                # 執行OCR識別
                result = ocr.ocr(char_img, cls=True)
                
                if result and result[0]:
                    # 提取識別結果
                    text = result[0][0][1][0]
                    confidence = result[0][0][1][1]
                    
                    # 過濾低置信度結果
                    if confidence >= self.confidence_threshold:
                        # 清理和驗證字符
                        cleaned_char = self._clean_character(text)
                        if cleaned_char:
                            results.append({
                                'character': cleaned_char,
                                'confidence': confidence,
                                'position': i
                            })
                            continue
                
                # 如果OCR失敗，使用模板匹配
                template_result = self._recognize_single_with_template(char_img)
                results.append(template_result)
                
            except Exception as e:
                print(f"PaddleOCR識別字符 {i} 失敗: {e}")
                # 使用模板匹配作為備用
                template_result = self._recognize_single_with_template(char_img)
                results.append(template_result)
        
        return results
    
    def _recognize_with_template(self, character_images):
        """使用模板匹配識別字符"""
        results = []
        
        for i, char_img in enumerate(character_images):
            result = self._recognize_single_with_template(char_img)
            result['position'] = i
            results.append(result)
        
        return results
    
    def _recognize_single_with_template(self, char_image):
        """單個字符的模板匹配識別"""
        try:
            # 標準化圖像
            normalized = self._normalize_character(char_image)
            
            # 提取特徵
            features = self._extract_features(normalized)
            
            # 匹配字符
            best_match = self._match_character(features)
            
            return {
                'character': best_match['char'],
                'confidence': best_match['confidence'],
                'method': 'template'
            }
            
        except Exception as e:
            print(f"模板匹配識別失敗: {e}")
            return {
                'character': '?',
                'confidence': 0.0,
                'method': 'template'
            }
    
    def _normalize_character(self, char_image):
        """標準化字符圖像"""
        # 調整大小
        resized = cv2.resize(char_image, (32, 32), interpolation=cv2.INTER_LINEAR)
        
        # 確保是二值圖像
        if len(resized.shape) == 3:
            gray = cv2.cvtColor(resized, cv2.COLOR_BGR2GRAY)
        else:
            gray = resized
        
        _, binary = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
        
        return binary
    
    def _extract_features(self, normalized_image):
        """提取字符特徵"""
        # 簡單的網格特徵
        h, w = normalized_image.shape
        grid_size = 8
        cell_h, cell_w = h // grid_size, w // grid_size
        
        features = []
        for i in range(grid_size):
            for j in range(grid_size):
                cell = normalized_image[i*cell_h:(i+1)*cell_h, j*cell_w:(j+1)*cell_w]
                # 計算單元格中的黑色像素比例
                black_ratio = np.sum(cell == 0) / (cell_h * cell_w)
                features.append(black_ratio)
        
        return np.array(features)
    
    def _match_character(self, features):
        """匹配字符"""
        # 簡化的字符模板
        templates = {
            '0': self._create_template('0'),
            '1': self._create_template('1'),
            '2': self._create_template('2'),
            '3': self._create_template('3'),
            '4': self._create_template('4'),
            '5': self._create_template('5'),
            '6': self._create_template('6'),
            '7': self._create_template('7'),
            '8': self._create_template('8'),
            '9': self._create_template('9'),
            'A': self._create_template('A'),
            'B': self._create_template('B'),
            'C': self._create_template('C'),
            'D': self._create_template('D'),
            'E': self._create_template('E'),
            'F': self._create_template('F'),
            'G': self._create_template('G'),
            'H': self._create_template('H'),
            'I': self._create_template('I'),
            'J': self._create_template('J'),
            'K': self._create_template('K'),
            'L': self._create_template('L'),
            'M': self._create_template('M'),
            'N': self._create_template('N'),
            'O': self._create_template('O'),
            'P': self._create_template('P'),
            'Q': self._create_template('Q'),
            'R': self._create_template('R'),
            'S': self._create_template('S'),
            'T': self._create_template('T'),
            'U': self._create_template('U'),
            'V': self._create_template('V'),
            'W': self._create_template('W'),
            'X': self._create_template('X'),
            'Y': self._create_template('Y'),
            'Z': self._create_template('Z')
        }
        
        best_char = '?'
        best_score = 0
        
        for char, template in templates.items():
            # 計算相似度
            score = np.corrcoef(features, template)[0, 1]
            if score > best_score:
                best_score = score
                best_char = char
        
        return {
            'char': best_char,
            'confidence': best_score
        }
    
    def _create_template(self, char):
        """創建字符模板"""
        # 簡化的模板生成
        template = np.zeros(64)  # 8x8網格
        
        # 這裡應該根據實際字符形狀生成模板
        # 為簡單起見，返回隨機模板
        if char.isdigit():
            # 數字模板
            template[:32] = 0.5
        else:
            # 字母模板
            template[32:] = 0.5
        
        return template
    
    def _clean_character(self, text):
        """清理和驗證字符"""
        if not text:
            return None
        
        # 只保留第一個字符
        char = text[0].upper()
        
        # 驗證字符是否在有效範圍內
        if char in self.characters['english'] or char in self.characters['numbers']:
            return char
        
        return None
    
    def combine_characters(self, recognition_results):
        """組合識別結果"""
        if not recognition_results:
            return ""
        
        # 按位置排序
        sorted_results = sorted(recognition_results, key=lambda x: x.get('position', 0))
        
        # 組合字符
        plate_text = ""
        for result in sorted_results:
            plate_text += result.get('character', '?')
        
        return plate_text
    
    def validate_plate_format(self, plate_text):
        """驗證車牌格式"""
        if not plate_text:
            return False
        
        # 台灣車牌格式驗證
        patterns = [
            r'^[A-Z]{2}-\d{4}$',      # 例如: AB-1234
            r'^[A-Z]{3}-\d{3}$',      # 例如: ABC-123
            r'^[A-Z]\d{2}-[A-Z]{2}$', # 例如: A12-BC
            r'^\d{4}-[A-Z]{2}$',      # 例如: 1234-AB
            r'^\d{3}-[A-Z]{3}$'       # 例如: 123-ABC
        ]
        
        for pattern in patterns:
            if re.match(pattern, plate_text):
                return True
        
        return False
    
    def get_recognition_confidence(self, recognition_results):
        """計算整體識別置信度"""
        if not recognition_results:
            return 0.0
        
        total_confidence = sum(result.get('confidence', 0) 
                             for result in recognition_results)
        average_confidence = total_confidence / len(recognition_results)
        
        return average_confidence