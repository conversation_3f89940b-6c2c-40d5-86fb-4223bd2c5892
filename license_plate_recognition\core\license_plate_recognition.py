import cv2
import numpy as np
import time
from pathlib import Path
from typing import List, Dict, Optional

from utils.config import Config
from utils.image_utils import save_image, get_image_info
from core.preprocessor import ImagePreprocessor
from core.detector import LicensePlateDetector
from core.segmenter import CharacterSegmenter
from core.recognizer import CharacterRecognizer

class LicensePlateRecognition:
    """車牌識別主類"""
    
    def __init__(self, 
                 detector_model_path=None,
                 recognizer_model_path=None,
                 save_results=True):
        """
        初始化車牌識別系統
        
        Args:
            detector_model_path: 檢測模型路徑
            recognizer_model_path: 識別模型路徑
            save_results: 是否保存結果
        """
        self.save_results = save_results
        
        # 初始化各個模組
        self.preprocessor = ImagePreprocessor()
        self.detector = LicensePlateDetector(detector_model_path)
        self.segmenter = CharacterSegmenter()
        self.recognizer = CharacterRecognizer(recognizer_model_path)
        
        # 統計信息
        self.processing_stats = {
            'total_images': 0,
            'successful_detections': 0,
            'successful_recognitions': 0,
            'average_processing_time': 0
        }
    
    def recognize_from_image(self, image_path: str) -> Dict:
        """
        從圖像文件識別車牌
        
        Args:
            image_path: 圖像文件路徑
            
        Returns:
            識別結果字典
        """
        start_time = time.time()
        
        try:
            # 載入圖像
            image = cv2.imread(str(image_path))
            if image is None:
                return self._create_error_result(image_path, "無法載入圖像文件")
            
            # 執行識別
            result = self._recognize_image(image, image_path)
            
            # 記錄處理時間
            processing_time = time.time() - start_time
            result['processing_time'] = processing_time
            
            # 更新統計信息
            self._update_stats(result)
            
            # 保存結果
            if self.save_results:
                self._save_result(result, image_path)
            
            return result
            
        except Exception as e:
            return self._create_error_result(image_path, f"處理過程中出錯: {str(e)}")
    
    def recognize_from_camera(self, camera_id=0, max_frames=10) -> Dict:
        """
        從攝像頭識別車牌
        
        Args:
            camera_id: 攝像頭ID
            max_frames: 最大幀數
            
        Returns:
            識別結果字典
        """
        cap = cv2.VideoCapture(camera_id)
        
        if not cap.isOpened():
            return self._create_error_result("camera", "無法打開攝像頭")
        
        best_result = None
        best_confidence = 0
        
        try:
            for frame_count in range(max_frames):
                ret, frame = cap.read()
                if not ret:
                    continue
                
                # 識別當前幀
                result = self._recognize_image(frame, f"camera_frame_{frame_count}")
                
                # 找到置信度最高的結果
                if result['success'] and result.get('confidence', 0) > best_confidence:
                    best_confidence = result['confidence']
                    best_result = result
                    best_result['frame_number'] = frame_count
                
                # 如果找到高質量的結果，提前停止
                if best_confidence > 0.8:
                    break
            
            cap.release()
            
            if best_result:
                return best_result
            else:
                return self._create_error_result("camera", "未識別到有效車牌")
                
        except Exception as e:
            cap.release()
            return self._create_error_result("camera", f"攝像頭處理出錯: {str(e)}")
    
    def batch_recognize(self, image_paths: List[str]) -> List[Dict]:
        """
        批量識別車牌
        
        Args:
            image_paths: 圖像文件路徑列表
            
        Returns:
            識別結果列表
        """
        results = []
        
        for i, image_path in enumerate(image_paths):
            print(f"處理第 {i+1}/{len(image_paths)} 張圖像: {image_path}")
            
            result = self.recognize_from_image(image_path)
            results.append(result)
            
            # 簡單的進度顯示
            if (i + 1) % 10 == 0:
                success_rate = sum(1 for r in results if r['success']) / len(results)
                print(f"進度: {i+1}/{len(image_paths)}, 成功率: {success_rate:.2%}")
        
        return results
    
    def _recognize_image(self, image: np.ndarray, source: str) -> Dict:
        """執行圖像識別的核心方法"""
        result = {
            'source': source,
            'success': False,
            'detections': [],
            'recognition_results': [],
            'plate_text': '',
            'confidence': 0,
            'processing_stages': {}
        }
        
        try:
            # 1. 車牌檢測
            detections = self.detector.detect(image)
            result['detections'] = detections
            result['processing_stages']['detection'] = len(detections)
            
            if not detections:
                result['error'] = '未檢測到車牌'
                return result
            
            # 使用置信度最高的檢測結果
            best_detection = max(detections, key=lambda x: x['confidence'])
            
            # 2. 提取車牌區域
            x1, y1, x2, y2 = best_detection['bbox']
            plate_region = image[y1:y2, x1:x2]
            result['processing_stages']['plate_extraction'] = True
            
            # 3. 預處理車牌圖像
            processed_plate = self.preprocessor.preprocess_for_ocr(plate_region)
            result['processing_stages']['preprocessing'] = True
            
            # 4. 字符分割
            characters = self.segmenter.segment_characters(processed_plate)
            result['processing_stages']['segmentation'] = len(characters)
            
            if not characters:
                result['error'] = '字符分割失敗'
                return result
            
            # 5. 字符識別
            recognition_results = self.recognizer.recognize_characters(characters)
            result['recognition_results'] = recognition_results
            result['processing_stages']['recognition'] = len(recognition_results)
            
            if not recognition_results:
                result['error'] = '字符識別失敗'
                return result
            
            # 6. 組合識別結果
            plate_text = self.recognizer.combine_characters(recognition_results)
            result['plate_text'] = plate_text
            
            # 7. 驗證格式
            is_valid_format = self.recognizer.validate_plate_format(plate_text)
            result['is_valid_format'] = is_valid_format
            
            # 8. 計算整體置信度
            confidence = self.recognizer.get_recognition_confidence(recognition_results)
            result['confidence'] = confidence
            
            # 9. 判斷是否成功
            result['success'] = (len(plate_text) >= 4 and 
                               confidence > 0.5 and 
                               is_valid_format)
            
            # 10. 保存中間結果
            if self.save_results:
                self._save_intermediate_results(result, image, plate_region, processed_plate, characters)
            
            return result
            
        except Exception as e:
            result['error'] = f"識別過程出錯: {str(e)}"
            return result
    
    def _create_error_result(self, source: str, error_message: str) -> Dict:
        """創建錯誤結果"""
        return {
            'source': source,
            'success': False,
            'error': error_message,
            'detections': [],
            'recognition_results': [],
            'plate_text': '',
            'confidence': 0,
            'processing_time': 0
        }
    
    def _update_stats(self, result: Dict):
        """更新統計信息"""
        self.processing_stats['total_images'] += 1
        
        if result.get('detections'):
            self.processing_stats['successful_detections'] += 1
        
        if result.get('success'):
            self.processing_stats['successful_recognitions'] += 1
        
        # 更新平均處理時間
        total_time = (self.processing_stats['average_processing_time'] * 
                     (self.processing_stats['total_images'] - 1) + 
                     result.get('processing_time', 0))
        self.processing_stats['average_processing_time'] = total_time / self.processing_stats['total_images']
    
    def _save_result(self, result: Dict, original_path: str):
        """保存識別結果"""
        try:
            # 創建結果文件名
            original_name = Path(original_path).stem
            result_file = Config.RESULTS_DIR / f"{original_name}_result.txt"
            
            # 保存文本結果
            with open(result_file, 'w', encoding='utf-8') as f:
                f.write(f"車牌號碼: {result.get('plate_text', '')}\n")
                f.write(f"置信度: {result.get('confidence', 0):.2%}\n")
                f.write(f"成功: {result.get('success', False)}\n")
                f.write(f"處理時間: {result.get('processing_time', 0):.3f}秒\n")
                
                if result.get('error'):
                    f.write(f"錯誤: {result.get('error')}\n")
            
            # 保存JSON格式的詳細結果
            import json
            json_file = Config.RESULTS_DIR / f"{original_name}_result.json"
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存結果失敗: {e}")
    
    def _save_intermediate_results(self, result: Dict, original_image: np.ndarray,
                                 plate_region: np.ndarray, processed_plate: np.ndarray,
                                 characters: list):
        """保存中間處理結果"""
        try:
            source_name = Path(result['source']).stem
            
            # 保存原始圖像（標記檢測框）
            if result.get('detections'):
                marked_image = original_image.copy()
                for detection in result['detections']:
                    x1, y1, x2, y2 = detection['bbox']
                    cv2.rectangle(marked_image, (x1, y1), (x2, y2), (0, 255, 0), 2)
                
                marked_path = Config.RESULTS_DIR / f"{source_name}_detected.jpg"
                save_image(marked_image, marked_path)
            
            # 保存車牌區域
            if plate_region.size > 0:
                plate_path = Config.RESULTS_DIR / f"{source_name}_plate.jpg"
                save_image(plate_region, plate_path)
            
            # 保存處理後的車牌
            if processed_plate.size > 0:
                processed_path = Config.RESULTS_DIR / f"{source_name}_processed.jpg"
                save_image(processed_plate, processed_path)
            
            # 保存分割的字符
            for i, char in enumerate(characters):
                char_path = Config.RESULTS_DIR / f"{source_name}_char_{i}.jpg"
                save_image(char, char_path)
                
        except Exception as e:
            print(f"保存中間結果失敗: {e}")
    
    def get_statistics(self) -> Dict:
        """獲取處理統計信息"""
        stats = self.processing_stats.copy()
        
        if stats['total_images'] > 0:
            stats['detection_rate'] = stats['successful_detections'] / stats['total_images']
            stats['recognition_rate'] = stats['successful_recognitions'] / stats['total_images']
        else:
            stats['detection_rate'] = 0
            stats['recognition_rate'] = 0
        
        return stats
    
    def reset_statistics(self):
        """重置統計信息"""
        self.processing_stats = {
            'total_images': 0,
            'successful_detections': 0,
            'successful_recognitions': 0,
            'average_processing_time': 0
        }